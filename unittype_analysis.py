import json
import re
from collections import defaultdict

def load_seller_data():
    """Load all seller data files to get unittype information"""
    sellers = ['chef', 'depot', 'greco', 'perf', 'sham', 'sysco', 'usfood']
    seller_data = {}
    
    for seller in sellers:
        try:
            with open(f'{seller}.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Create a lookup dictionary by product number
                seller_data[seller] = {}
                for product in data:
                    product_num = product.get('productnumber', '')
                    if product_num:
                        seller_data[seller][product_num] = product
                print(f"Loaded {len(seller_data[seller])} products from {seller}.json")
        except Exception as e:
            print(f"Error loading {seller}.json: {e}")
            seller_data[seller] = {}
    
    return seller_data

def parse_key(key):
    """Parse a key like 'chef_5411046' or 'usfood_#2791630' to extract seller and product number"""
    if key.startswith('usfood_#'):
        return 'usfood', key[8:]  # Remove 'usfood_#'
    else:
        parts = key.split('_', 1)
        if len(parts) == 2:
            return parts[0], parts[1]
    return None, None

def get_unittype(seller_data, seller, product_num):
    """Get unittype for a specific product"""
    if seller in seller_data and product_num in seller_data[seller]:
        return seller_data[seller][product_num].get('unittype', '')
    return None

def analyze_unittype_uniformity():
    """Analyze unittype uniformity in matches.json"""
    
    # Load seller data
    print("Loading seller data...")
    seller_data = load_seller_data()
    
    # Load matches
    print("Loading matches.json...")
    with open('matches.json', 'r', encoding='utf-8') as f:
        matches = json.load(f)
    
    total_groups = len(matches)
    print(f"Total groups to analyze: {total_groups}")
    
    # Analysis counters
    complete_uniformity_count = 0
    per_seller_consistency_count = 0
    
    # Examples for reporting
    complete_uniformity_examples = []
    per_seller_consistency_examples = []
    
    # Detailed statistics
    groups_with_missing_data = 0
    
    print("Analyzing groups...")
    for i, group in enumerate(matches):
        if i % 5000 == 0:
            print(f"Processed {i}/{total_groups} groups...")
        
        # Extract unittype for each product in the group
        group_unittypes = []
        seller_unittypes = defaultdict(list)  # seller -> list of unittypes
        missing_data = False
        
        for key, product_name in group.items():
            seller, product_num = parse_key(key)
            if seller and product_num:
                unittype = get_unittype(seller_data, seller, product_num)
                if unittype is not None:
                    group_unittypes.append(unittype)
                    seller_unittypes[seller].append(unittype)
                else:
                    missing_data = True
        
        if missing_data:
            groups_with_missing_data += 1
        
        # Skip groups with no valid unittype data
        if not group_unittypes:
            continue
        
        # Question 1: Complete Unittype Uniformity
        # Check if ALL products have the same unittype
        unique_unittypes = set(group_unittypes)
        if len(unique_unittypes) == 1:
            complete_uniformity_count += 1
            if len(complete_uniformity_examples) < 3:
                complete_uniformity_examples.append({
                    'group_index': i,
                    'unittype': list(unique_unittypes)[0],
                    'products': dict(group),
                    'product_count': len(group)
                })
        
        # Question 2: Per-Seller Unittype Consistency
        # Check if there's at least one unittype that appears in all sellers
        if len(seller_unittypes) > 1:  # Only meaningful if multiple sellers
            # Get all unittypes that appear in each seller
            seller_unittype_sets = {seller: set(unittypes) for seller, unittypes in seller_unittypes.items()}
            
            # Find intersection of all seller unittype sets
            common_unittypes = set.intersection(*seller_unittype_sets.values()) if seller_unittype_sets else set()
            
            if common_unittypes:
                per_seller_consistency_count += 1
                if len(per_seller_consistency_examples) < 3:
                    per_seller_consistency_examples.append({
                        'group_index': i,
                        'common_unittypes': list(common_unittypes),
                        'seller_unittypes': dict(seller_unittype_sets),
                        'products': dict(group),
                        'seller_count': len(seller_unittypes)
                    })
    
    # Calculate percentages
    complete_uniformity_percentage = (complete_uniformity_count / total_groups) * 100
    per_seller_consistency_percentage = (per_seller_consistency_count / total_groups) * 100
    
    # Print results
    print("\n" + "="*80)
    print("UNITTYPE UNIFORMITY ANALYSIS RESULTS")
    print("="*80)
    
    print(f"\nTotal groups analyzed: {total_groups:,}")
    print(f"Groups with missing unittype data: {groups_with_missing_data:,}")
    
    print(f"\n{'='*50}")
    print("QUESTION 1: COMPLETE UNITTYPE UNIFORMITY")
    print(f"{'='*50}")
    print(f"Groups where ALL products have identical unittype: {complete_uniformity_count:,}")
    print(f"Percentage: {complete_uniformity_percentage:.2f}%")
    
    if complete_uniformity_examples:
        print(f"\nExamples of groups with complete uniformity:")
        for i, example in enumerate(complete_uniformity_examples, 1):
            print(f"\nExample {i}:")
            print(f"  Unittype: {example['unittype']}")
            print(f"  Products in group: {example['product_count']}")
            print(f"  Sample products:")
            for key, name in list(example['products'].items())[:3]:
                print(f"    {key}: {name}")
    
    print(f"\n{'='*50}")
    print("QUESTION 2: PER-SELLER UNITTYPE CONSISTENCY")
    print(f"{'='*50}")
    print(f"Groups with at least one unittype common across all sellers: {per_seller_consistency_count:,}")
    print(f"Percentage: {per_seller_consistency_percentage:.2f}%")
    
    if per_seller_consistency_examples:
        print(f"\nExamples of groups with per-seller consistency:")
        for i, example in enumerate(per_seller_consistency_examples, 1):
            print(f"\nExample {i}:")
            print(f"  Common unittype(s): {example['common_unittypes']}")
            print(f"  Sellers in group: {example['seller_count']}")
            print(f"  Unittype by seller:")
            for seller, unittypes in example['seller_unittypes'].items():
                print(f"    {seller}: {list(unittypes)}")
            print(f"  Sample products:")
            for key, name in list(example['products'].items())[:3]:
                print(f"    {key}: {name}")
    
    # Save detailed results to JSON
    results = {
        'analysis_summary': {
            'total_groups': total_groups,
            'groups_with_missing_data': groups_with_missing_data,
            'complete_uniformity': {
                'count': complete_uniformity_count,
                'percentage': complete_uniformity_percentage
            },
            'per_seller_consistency': {
                'count': per_seller_consistency_count,
                'percentage': per_seller_consistency_percentage
            }
        },
        'examples': {
            'complete_uniformity': complete_uniformity_examples,
            'per_seller_consistency': per_seller_consistency_examples
        }
    }
    
    with open('unittype_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n{'='*50}")
    print("Detailed results saved to: unittype_analysis_results.json")
    print(f"{'='*50}")

if __name__ == "__main__":
    analyze_unittype_uniformity()
