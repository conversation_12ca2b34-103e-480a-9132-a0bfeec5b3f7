const fs = require('fs').promises;

async function analyzeSyscoLB() {
  try {
    // Read the JSON file
    const data = await fs.readFile('sysco_lb.json', 'utf8');
    const products = JSON.parse(data);

    // Initialize analytics object
    const analytics = {
      totalProducts: products.length,
      missingOrEmptyLBquantity: 0,
      missingOrEmptyLBquantityPercentage: 0,
      unittypes: {}
    };

    // Process each product
    products.forEach(product => {
      // Check for missing or empty LBquantity
      if (!product.hasOwnProperty('LBquantity') || product.LBquantity === '' || product.LBquantity === null || product.LBquantity === undefined) {
        analytics.missingOrEmptyLBquantity++;
      }

      // Count unique unittypes and their LBquantity status
      const unittype = product.unittype || 'undefined';
      if (!analytics.unittypes[unittype]) {
        analytics.unittypes[unittype] = {
          totalCount: 0,
          nonEmptyLBquantityCount: 0
        };
      }
      analytics.unittypes[unittype].totalCount++;
      if (product.hasOwnProperty('LBquantity') && product.LBquantity !== '' && product.LBquantity !== null && product.LBquantity !== undefined) {
        analytics.unittypes[unittype].nonEmptyLBquantityCount++;
      }
    });

    // Calculate percentage of missing/empty LBquantity
    analytics.missingOrEmptyLBquantityPercentage = (analytics.missingOrEmptyLBquantity / analytics.totalProducts * 100).toFixed(2);

    // Write results to output file
    await fs.writeFile('sysco_lb_analytics.json', JSON.stringify(analytics, null, 2));
    console.log('Analytics complete. Results written to sysco_lb_analytics.json');

  } catch (error) {
    console.error('Error processing file:', error.message);
  }
}

// Run the analysis
analyzeSyscoLB();