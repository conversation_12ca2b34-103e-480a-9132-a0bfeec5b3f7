import json
import re

def extract_unit_type(unit_str):
    # Find the last word before the closing parenthesis
    match = re.search(r'(\d+\s+)?([a-zA-Z]+)\)$', unit_str)
    if match:
        return match.group(2)
    return None

# Load your JSON file
with open('perf.json', 'r') as f:
    data = json.load(f)

unit_types = {}

for product in data:
    unit = product.get('unit', '')
    if not unit:
        continue
    unit_type = extract_unit_type(unit)
    if unit_type:
        # Initialize if not present
        if unit_type not in unit_types:
            unit_types[unit_type] = {"count": 0, "example": None}
        unit_types[unit_type]["count"] += 1
        # Save the first example for each unit_type
        if unit_types[unit_type]["example"] is None:
            # Assuming productnumber and name are the keys. Adjust as needed!
            productnumber = product.get('productnumber', '')
            name = product.get('name', '')
            if productnumber and name:
                unit_types[unit_type]["example"] = {productnumber: name}

# Prepare output
output = {"unit_types": unit_types}

# Save to a new JSON file
with open('unit_types_summary.json', 'w') as f:
    json.dump(output, f, indent=2)

print("Results saved to unit_types_summary.json")
