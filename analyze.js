const fs = require('fs').promises;

async function analyzeTransformedJson(inputFile, outputFile) {
  try {
    // Read the input JSON file
    const data = await fs.readFile(inputFile, 'utf8');
    const jsonArray = JSON.parse(data);

    // Initialize collectors
    const propertyCounts = {};
    const uniqueUnitTypes = new Set();

    // Analyze each object
    jsonArray.forEach(item => {
      // Get all properties in the current object
      Object.keys(item).forEach(prop => {
        // Initialize counts for new properties
        if (!propertyCounts[prop]) {
          propertyCounts[prop] = { empty: 0, total: 0 };
        }

        // Increment total count
        propertyCounts[prop].total++;

        // Check for empty values
        if (item[prop] === '' || item[prop] === null || item[prop] === undefined ||
            item[prop] === 'Not found' ||
            ((prop === 'unitWeight' || prop === 'caseWeight') && item[prop] === 0)) {
          propertyCounts[prop].empty++;
        }

        // Collect unique unitType values
        if (prop === 'unittype' && item[prop] && item[prop] !== '' && item[prop] !== 'Not found') {
          uniqueUnitTypes.add(item[prop]);
        }
      });
    });

    // Calculate percentages for empty values
    const emptyStats = {};
    Object.keys(propertyCounts).forEach(prop => {
      const { empty, total } = propertyCounts[prop];
      emptyStats[prop] = {
        emptyCount: empty,
        emptyPercentage: total > 0 ? ((empty / total) * 100).toFixed(2) + '%' : '0.00%'
      };
    });

    // Prepare analysis output
    const analysis = {
      emptyValues: emptyStats,
      uniqueValues: {
        unitType: [...uniqueUnitTypes]
      }
    };

    // Write to output JSON file
    await fs.writeFile(outputFile, JSON.stringify(analysis, null, 2));
    console.log(`Analysis written to ${outputFile}`);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run the analysis
analyzeTransformedJson('perf_o.json', 'perfo_analysis.json');