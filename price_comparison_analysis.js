const fs = require('fs').promises;
const {
  loadSellerData,
  extractPriceProperties,
  calculatePrice,
  calculatePriceDifference,
  parseMatchKey
} = require('./price_analysis');

async function analyzePriceComparisons() {
  console.log('Starting price comparison analysis...');

  // Load data
  console.log('Loading seller data...');
  const sellerData = await loadSellerData();

  console.log('Loading matches.json...');
  const matchesData = await fs.readFile('matches.json', 'utf8');
  const matches = JSON.parse(matchesData);

  console.log(`Processing ${matches.length} product groups...`);

  const results = {
    productGroups: [],
    summary: {
      totalGroups: matches.length,
      processedGroups: 0,
      groupsWithPrices: 0,
      closeMatches: 0,
      notCloseMatches: 0,
      averagePriceVariance: 0
    }
  };

  let totalVariance = 0;
  let groupsWithVariance = 0;

  for (let i = 0; i < matches.length; i++) {
    const group = matches[i];
    const groupId = `group_${i + 1}`;

    if (i % 1000 === 0) {
      console.log(`Processing group ${i + 1}/${matches.length}...`);
    }

    const productGroup = {
      groupId,
      products: [],
      groupAnalysis: {
        hasValidPrices: false,
        cheapestProduct: null,
        maxPriceVariance: 0,
        groupStatus: 'not close'
      }
    };

    const validProducts = [];

    // Process each product in the group
    for (const [matchKey, productName] of Object.entries(group)) {
      const parsed = parseMatchKey(matchKey);
      if (!parsed) continue;

      const { seller, productNumber } = parsed;
      const product = sellerData[seller]?.[productNumber];

      if (!product) continue;

      // Calculate prices using rules from memory
      const priceCalc = calculatePrice(product, seller);
      if (!priceCalc) continue; // Skip products with empty prices

      // For chef products, ensure we use the calculated prices
      if (seller === 'chef' && priceCalc.price === 0 && priceCalc.portionPrice === 0) {
        const unitPrice = parseFloat(product.unitprice) || 0;
        const casePrice = parseFloat(product.caseprice) || 0;
        const unitPortionPrice = parseFloat(product.unitportionprice) || 0;
        const casePortionPrice = parseFloat(product.caseportionprice) || 0;
        const caseQuantity = parseFloat(product.casequantity) || 1;

        if (casePrice > 0 && casePortionPrice > 0) {
          priceCalc.price = casePrice;
          priceCalc.portionPrice = casePortionPrice;
          priceCalc.quantity = caseQuantity;
        } else if (unitPrice > 0 && unitPortionPrice > 0) {
          priceCalc.price = unitPrice;
          priceCalc.portionPrice = unitPortionPrice;
          priceCalc.quantity = parseFloat(product.unitquantity) || 1;
        }
      }

      const { priceProps, portionPriceProps } = extractPriceProperties(product);

      const productAnalysis = {
        originalProductData: {
          ...product,
          seller,
          matchKey,
          productName
        },
        priceAnalysis: {
          regularPrice: priceCalc.price,
          portionPrice: priceCalc.portionPrice,
          unittype: priceCalc.unittype,
          quantity: priceCalc.quantity,
          allPriceProperties: { ...priceProps, ...portionPriceProps },
          isCheapest: false,
          priceStatus: 'close',
          priceVariancePercentage: 0
        }
      };

      validProducts.push(productAnalysis);
      productGroup.products.push(productAnalysis);
    }

    // Analyze price relationships within the group
    if (validProducts.length > 1) {
      productGroup.groupAnalysis.hasValidPrices = true;
      results.summary.groupsWithPrices++;

      // Find cheapest product and calculate variances
      let cheapestProduct = null;
      let cheapestPrice = Infinity;
      let maxVariance = 0;

      // Determine comparison price (prefer portion price, fall back to regular price)
      validProducts.forEach(product => {
        const comparisonPrice = product.priceAnalysis.portionPrice > 0
          ? product.priceAnalysis.portionPrice
          : product.priceAnalysis.regularPrice;

        if (comparisonPrice > 0 && comparisonPrice < cheapestPrice) {
          cheapestPrice = comparisonPrice;
          cheapestProduct = product;
        }
      });

      // Calculate price variances and mark weak matches
      validProducts.forEach(product => {
        const comparisonPrice = product.priceAnalysis.portionPrice > 0
          ? product.priceAnalysis.portionPrice
          : product.priceAnalysis.regularPrice;

        if (comparisonPrice > 0 && cheapestPrice > 0) {
          const variance = calculatePriceDifference(comparisonPrice, cheapestPrice);
          product.priceAnalysis.priceVariancePercentage = variance;

          if (variance > 40) {
            product.priceAnalysis.priceStatus = 'not close';
            maxVariance = Math.max(maxVariance, variance);
          }
        }
      });

      // Mark cheapest product (excluding weak matches)
      const strongMatches = validProducts.filter(p => p.priceAnalysis.priceStatus === 'close');
      if (strongMatches.length > 0) {
        let cheapestStrong = strongMatches[0];
        let cheapestStrongPrice = cheapestStrong.priceAnalysis.portionPrice > 0
          ? cheapestStrong.priceAnalysis.portionPrice
          : cheapestStrong.priceAnalysis.regularPrice;

        strongMatches.forEach(product => {
          const price = product.priceAnalysis.portionPrice > 0
            ? product.priceAnalysis.portionPrice
            : product.priceAnalysis.regularPrice;

          if (price > 0 && price < cheapestStrongPrice) {
            cheapestStrongPrice = price;
            cheapestStrong = product;
          }
        });

        cheapestStrong.priceAnalysis.isCheapest = true;
        productGroup.groupAnalysis.cheapestProduct = cheapestStrong.originalProductData.matchKey;
      }

      productGroup.groupAnalysis.maxPriceVariance = maxVariance;
      productGroup.groupAnalysis.groupStatus = maxVariance <= 40 ? 'close' : 'not close';

      if (maxVariance <= 40) {
        results.summary.closeMatches++;
      } else {
        results.summary.notCloseMatches++;
      }

      totalVariance += maxVariance;
      groupsWithVariance++;
    }

    results.productGroups.push(productGroup);
    results.summary.processedGroups++;
  }

  // Calculate summary statistics
  if (groupsWithVariance > 0) {
    results.summary.averagePriceVariance = totalVariance / groupsWithVariance;
  }

  console.log('\nAnalysis Summary:');
  console.log(`Total groups: ${results.summary.totalGroups}`);
  console.log(`Groups with valid prices: ${results.summary.groupsWithPrices}`);
  console.log(`Close matches: ${results.summary.closeMatches}`);
  console.log(`Not close matches: ${results.summary.notCloseMatches}`);
  console.log(`Average price variance: ${results.summary.averagePriceVariance.toFixed(2)}%`);

  // Create a more manageable output by filtering and limiting data
  const filteredResults = {
    productGroups: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices && group.products.length > 1)
      .slice(0, 1000) // Limit to first 1000 groups for web app performance
      .map(group => ({
        ...group,
        products: group.products.map(product => ({
          originalProductData: {
            seller: product.originalProductData.seller,
            matchKey: product.originalProductData.matchKey,
            productName: product.originalProductData.productName,
            productnumber: product.originalProductData.productnumber,
            name: product.originalProductData.name,
            packsize: product.originalProductData.packsize || product.originalProductData['Pack Size']
          },
          priceAnalysis: product.priceAnalysis
        }))
      })),
    summary: results.summary
  };

  // Save results
  console.log('\nSaving results to price_comparison_results.json...');
  await fs.writeFile('price_comparison_results.json', JSON.stringify(filteredResults, null, 2));

  // Also save a summary report
  const summaryReport = {
    analysisDate: new Date().toISOString(),
    summary: results.summary,
    topCheapestProducts: results.productGroups
      .filter(group => group.groupAnalysis.hasValidPrices)
      .map(group => {
        const cheapest = group.products.find(p => p.priceAnalysis.isCheapest);
        return cheapest ? {
          groupId: group.groupId,
          productName: cheapest.originalProductData.productName,
          seller: cheapest.originalProductData.seller,
          portionPrice: cheapest.priceAnalysis.portionPrice,
          regularPrice: cheapest.priceAnalysis.regularPrice,
          groupVariance: group.groupAnalysis.maxPriceVariance
        } : null;
      })
      .filter(item => item !== null)
      .sort((a, b) => a.portionPrice - b.portionPrice)
      .slice(0, 100)
  };

  await fs.writeFile('price_analysis_summary.json', JSON.stringify(summaryReport, null, 2));

  console.log('Price comparison analysis completed!');
  console.log(`Saved ${filteredResults.productGroups.length} product groups to web app data file`);
  return results;
}

// Run the analysis
if (require.main === module) {
  analyzePriceComparisons().catch(console.error);
}

module.exports = { analyzePriceComparisons };
