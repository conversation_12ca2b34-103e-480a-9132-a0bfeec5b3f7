const fs = require('fs');

async function quickValidation() {
    console.log('🔍 QUICK VALIDATION FOR chef_5411046');
    console.log('====================================\n');

    // Load data
    const originalMatches = JSON.parse(fs.readFileSync('matches.json', 'utf8'));
    const deduplicatedMatches = JSON.parse(fs.readFileSync('matches-sh.json', 'utf8'));

    console.log('📂 Data loaded successfully\n');

    // Find chef_5411046 in deduplicated matches
    let deduplicatedGroup = null;
    let deduplicatedGroupIndex = -1;

    for (let i = 0; i < deduplicatedMatches.length; i++) {
        if (deduplicatedMatches[i]['chef_5411046']) {
            deduplicatedGroup = deduplicatedMatches[i];
            deduplicatedGroupIndex = i;
            break;
        }
    }

    if (!deduplicatedGroup) {
        console.log('❌ chef_5411046 not found in deduplicated matches!');
        return;
    }

    console.log('🔍 FOUND chef_5411046 in deduplicated matches:');
    console.log(`   Group index: ${deduplicatedGroupIndex}`);
    console.log(`   Products in group: ${Object.keys(deduplicatedGroup).length}`);
    
    // Count sellers in deduplicated group
    const deduplicatedSellers = new Set();
    Object.keys(deduplicatedGroup).forEach(key => {
        deduplicatedSellers.add(key.split('_')[0]);
    });
    console.log(`   Sellers in group: ${deduplicatedSellers.size} (${Array.from(deduplicatedSellers).join(', ')})`);
    
    console.log('\n   Products in deduplicated group:');
    Object.entries(deduplicatedGroup).forEach(([key, name]) => {
        console.log(`     ${key}: ${name}`);
    });

    // Check if this exact group exists in original matches
    console.log('\n🔍 CHECKING if this group exists in original matches...');
    let foundInOriginal = false;
    let originalGroupIndex = -1;

    for (let i = 0; i < originalMatches.length; i++) {
        const originalGroup = originalMatches[i];
        
        // Check if groups are identical
        const originalKeys = Object.keys(originalGroup).sort();
        const deduplicatedKeys = Object.keys(deduplicatedGroup).sort();
        
        if (originalKeys.length === deduplicatedKeys.length) {
            let identical = true;
            for (let j = 0; j < originalKeys.length; j++) {
                if (originalKeys[j] !== deduplicatedKeys[j]) {
                    identical = false;
                    break;
                }
            }
            
            if (identical) {
                foundInOriginal = true;
                originalGroupIndex = i;
                break;
            }
        }
    }

    if (foundInOriginal) {
        console.log(`   ✅ FOUND identical group in original matches at index ${originalGroupIndex}`);
    } else {
        console.log(`   ❌ IDENTICAL group NOT FOUND in original matches!`);
        console.log(`   🚨 This is a GROUP INTEGRITY VIOLATION!`);
    }

    // Find all groups containing chef_5411046 in original matches
    console.log('\n🔍 FINDING all groups containing chef_5411046 in original matches...');
    const originalGroupsWithProduct = [];

    for (let i = 0; i < originalMatches.length; i++) {
        if (originalMatches[i]['chef_5411046']) {
            const group = originalMatches[i];
            const sellers = new Set();
            Object.keys(group).forEach(key => {
                sellers.add(key.split('_')[0]);
            });
            
            originalGroupsWithProduct.push({
                index: i,
                productCount: Object.keys(group).length,
                sellerCount: sellers.size,
                sellers: Array.from(sellers)
            });
        }
    }

    console.log(`   Found ${originalGroupsWithProduct.length} groups containing chef_5411046:`);
    
    // Sort by seller count (descending), then by product count (descending)
    originalGroupsWithProduct.sort((a, b) => {
        if (b.sellerCount !== a.sellerCount) {
            return b.sellerCount - a.sellerCount;
        }
        return b.productCount - a.productCount;
    });

    originalGroupsWithProduct.forEach((groupInfo, index) => {
        const isChosen = groupInfo.index === originalGroupIndex;
        console.log(`     ${index + 1}. Group ${groupInfo.index}: ${groupInfo.sellerCount} sellers, ${groupInfo.productCount} products ${isChosen ? '← CHOSEN' : ''}`);
        console.log(`        Sellers: ${groupInfo.sellers.join(', ')}`);
    });

    // Analysis
    console.log('\n📊 ANALYSIS:');
    const bestGroup = originalGroupsWithProduct[0];
    const chosenGroup = originalGroupsWithProduct.find(g => g.index === originalGroupIndex);

    if (!chosenGroup) {
        console.log('   ❌ CRITICAL ERROR: Chosen group not found in original groups!');
    } else {
        console.log(`   Best available: ${bestGroup.sellerCount} sellers, ${bestGroup.productCount} products (Group ${bestGroup.index})`);
        console.log(`   Actually chosen: ${chosenGroup.sellerCount} sellers, ${chosenGroup.productCount} products (Group ${chosenGroup.index})`);
        
        if (chosenGroup.sellerCount < bestGroup.sellerCount) {
            console.log('   ❌ SELLER DIVERSITY VIOLATION: Chosen group has fewer sellers than best available!');
        } else if (chosenGroup.sellerCount === bestGroup.sellerCount && chosenGroup.productCount < bestGroup.productCount) {
            console.log('   ❌ GROUP SIZE VIOLATION: Chosen group has same sellers but fewer products than best available!');
        } else if (chosenGroup.index === bestGroup.index) {
            console.log('   ✅ CORRECT: Chosen group is the optimal choice!');
        } else {
            console.log('   ⚠️  Chosen group is valid but not the absolute best');
        }
    }

    console.log('\n🎯 CONCLUSION:');
    if (!foundInOriginal) {
        console.log('   ❌ MAJOR BUG: Group integrity violated - deduplicated group does not exist in original!');
    } else if (!chosenGroup || chosenGroup.sellerCount < bestGroup.sellerCount) {
        console.log('   ❌ ALGORITHM BUG: Seller diversity rule not followed!');
    } else {
        console.log('   ✅ Algorithm appears to be working correctly for this product');
    }
}

quickValidation().catch(console.error);
