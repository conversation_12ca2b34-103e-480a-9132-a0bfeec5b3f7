import json

# Load your usfood.json file
with open('usfood.json', 'r', encoding='utf-8') as f:
    data = json.load(f)


unit_stats = {}

for product in data:
    if "unit" in product:
        unit = product["unit"]
        if unit not in unit_stats:
            unit_stats[unit] = {
                "count": 0,
                "example": None
            }
        unit_stats[unit]["count"] += 1
        # Save the first example for each unit
        if unit_stats[unit]["example"] is None:
            # Adjust these keys to match your JSON!
            productnumber = product.get("productnumber", "")
            name = product.get("name", "")
            if productnumber and name:
                unit_stats[unit]["example"] = {productnumber: name}
            # If you want the full product as example, use:
            # unit_stats[unit]["example"] = product.copy()

# Prepare output
output = {"units": unit_stats}

# Save to a new JSON file
with open('unit_summary_usfood.json', 'w') as f:
    json.dump(output, f, indent=2)

print("Results saved to unit_summary_usfood.json")
