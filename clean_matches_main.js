const fs = require('fs').promises;
const {
  normalizeProductName,
  extractProductFeatures,
  calculateProductSimilarity,
  findCoreGroupCharacteristics,
  productFitsGroup,
  FOOD_CATEGORIES,
  CONFLICTING_KEYWORDS
} = require('./clean_matches');

async function cleanMatches() {
  console.log('🧹 Starting matches.json cleanup process...');
  console.log('=====================================\n');

  try {
    // Load matches.json
    console.log('📂 Loading matches.json...');
    const matchesData = await fs.readFile('matches.json', 'utf8');
    const matches = JSON.parse(matchesData);
    console.log(`   Loaded ${matches.length} product groups\n`);

    const cleaningResults = {
      originalGroups: matches.length,
      processedGroups: 0,
      groupsModified: 0,
      groupsRemoved: 0,
      productsRemoved: 0,
      cleanedGroups: [],
      removedProducts: [],
      groupAnalysis: []
    };

    const cleanedMatches = [];

    // Process each group
    for (let i = 0; i < matches.length; i++) {
      const group = matches[i];
      const groupId = `group_${i + 1}`;
      
      if (i % 1000 === 0) {
        console.log(`🔍 Processing group ${i + 1}/${matches.length}...`);
      }

      // Skip groups with less than 2 products
      const productCount = Object.keys(group).length;
      if (productCount < 2) {
        cleanedMatches.push(group);
        cleaningResults.processedGroups++;
        continue;
      }

      // Analyze the group
      const coreCharacteristics = findCoreGroupCharacteristics(group);
      
      // Determine which products fit the core group
      const fittingProducts = {};
      const removedFromGroup = [];
      
      for (const [productKey, productName] of Object.entries(group)) {
        const fits = productFitsGroup(productName, coreCharacteristics, 50); // Lower threshold for more inclusive matching
        
        if (fits) {
          fittingProducts[productKey] = productName;
        } else {
          removedFromGroup.push({
            key: productKey,
            name: productName,
            reason: 'Does not match core group characteristics'
          });
          cleaningResults.productsRemoved++;
        }
      }

      // Only keep groups with at least 2 products after cleaning
      if (Object.keys(fittingProducts).length >= 2) {
        cleanedMatches.push(fittingProducts);
        
        if (removedFromGroup.length > 0) {
          cleaningResults.groupsModified++;
        }
      } else {
        cleaningResults.groupsRemoved++;
        // Add all products to removed list
        for (const [productKey, productName] of Object.entries(group)) {
          removedFromGroup.push({
            key: productKey,
            name: productName,
            reason: 'Group too small after cleaning'
          });
          cleaningResults.productsRemoved++;
        }
      }

      // Store analysis for reporting
      cleaningResults.groupAnalysis.push({
        groupId,
        originalSize: productCount,
        cleanedSize: Object.keys(fittingProducts).length,
        coreCharacteristics,
        removedProducts: removedFromGroup
      });

      cleaningResults.removedProducts.push(...removedFromGroup);
      cleaningResults.processedGroups++;
    }

    // Generate summary
    cleaningResults.finalGroups = cleanedMatches.length;
    cleaningResults.finalProducts = cleanedMatches.reduce((sum, group) => sum + Object.keys(group).length, 0);

    console.log('\n📊 CLEANING SUMMARY');
    console.log('==================');
    console.log(`Original groups: ${cleaningResults.originalGroups}`);
    console.log(`Final groups: ${cleaningResults.finalGroups}`);
    console.log(`Groups modified: ${cleaningResults.groupsModified}`);
    console.log(`Groups removed: ${cleaningResults.groupsRemoved}`);
    console.log(`Products removed: ${cleaningResults.productsRemoved}`);
    console.log(`Reduction: ${((cleaningResults.originalGroups - cleaningResults.finalGroups) / cleaningResults.originalGroups * 100).toFixed(1)}%`);

    // Save cleaned matches
    console.log('\n💾 Saving cleaned matches...');
    await fs.writeFile('matches_cleaned.json', JSON.stringify(cleanedMatches, null, 2));
    console.log('   ✅ Saved to matches_cleaned.json');

    // Save detailed report
    await fs.writeFile('cleaning_report.json', JSON.stringify(cleaningResults, null, 2));
    console.log('   ✅ Saved detailed report to cleaning_report.json');

    // Generate human-readable report
    await generateReadableReport(cleaningResults);
    console.log('   ✅ Saved readable report to cleaning_summary.md');

    console.log('\n🎉 Cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  }
}

async function generateReadableReport(results) {
  const report = `# Matches Cleaning Report

## Summary
- **Original groups**: ${results.originalGroups.toLocaleString()}
- **Final groups**: ${results.finalGroups.toLocaleString()}
- **Groups modified**: ${results.groupsModified.toLocaleString()}
- **Groups removed**: ${results.groupsRemoved.toLocaleString()}
- **Products removed**: ${results.productsRemoved.toLocaleString()}
- **Reduction percentage**: ${((results.originalGroups - results.finalGroups) / results.originalGroups * 100).toFixed(1)}%

## Top Removal Reasons
${generateTopRemovalReasons(results)}

## Sample Cleaned Groups
${generateSampleCleanedGroups(results)}

## Sample Removed Products
${generateSampleRemovedProducts(results)}
`;

  await fs.writeFile('cleaning_summary.md', report);
}

function generateTopRemovalReasons(results) {
  const reasons = {};
  results.removedProducts.forEach(product => {
    reasons[product.reason] = (reasons[product.reason] || 0) + 1;
  });

  return Object.entries(reasons)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([reason, count]) => `- **${reason}**: ${count.toLocaleString()} products`)
    .join('\n');
}

function generateSampleCleanedGroups(results) {
  const modifiedGroups = results.groupAnalysis
    .filter(group => group.removedProducts.length > 0)
    .slice(0, 5);

  return modifiedGroups.map(group => {
    return `### ${group.groupId}
- **Original size**: ${group.originalSize} products
- **Cleaned size**: ${group.cleanedSize} products
- **Core category**: ${group.coreCharacteristics.dominantCategory}
- **Core type**: ${group.coreCharacteristics.dominantType}
- **Removed**: ${group.removedProducts.length} products`;
  }).join('\n\n');
}

function generateSampleRemovedProducts(results) {
  return results.removedProducts
    .slice(0, 10)
    .map(product => `- **${product.key}**: ${product.name} (${product.reason})`)
    .join('\n');
}

// Run the cleaning process
if (require.main === module) {
  cleanMatches().catch(console.error);
}

module.exports = { cleanMatches };
