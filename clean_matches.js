const fs = require('fs').promises;

// Common food categories and their keywords
const FOOD_CATEGORIES = {
  'cheese_sticks': ['mozzarella', 'cheese stick', 'mozz stick'],
  'mac_cheese': ['mac', 'macaroni', 'cheese wedge', 'mac & cheese', 'mac and cheese'],
  'jalapeno_poppers': ['jalapeno', 'popper', 'pepper'],
  'onion_rings': ['onion ring', 'onion'],
  'cheese_curds': ['curd', 'cheese curd'],
  'chicken_wings': ['wing', 'chicken wing'],
  'chicken_tenders': ['tender', 'chicken tender', 'chicken strip'],
  'fries': ['fries', 'french fries', 'potato'],
  'ravioli': ['ravioli'],
  'breadsticks': ['breadstick', 'bread stick'],
  'appetizers': ['appetizer', 'apptzr']
};

// Words that indicate different product types (should not be mixed)
const CONFLICTING_KEYWORDS = [
  ['mozzarella', 'cheddar', 'pepper jack'],
  ['mac', 'onion', 'jalapeno', 'chicken'],
  ['stick', 'ring', 'bite', 'wedge', 'curd'],
  ['breaded', 'battered', 'fried'],
  ['frozen', 'fresh']
];

// Function to normalize product names for comparison
function normalizeProductName(name) {
  if (!name || typeof name !== 'string') return '';

  return name.toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

// Function to extract key ingredients and product type from name
function extractProductFeatures(name) {
  const normalized = normalizeProductName(name);
  const words = normalized.split(' ');

  const features = {
    ingredients: [],
    productType: '',
    preparation: [],
    size: '',
    category: ''
  };

  // Extract ingredients
  const ingredients = ['mozzarella', 'cheddar', 'cheese', 'jalapeno', 'onion', 'chicken', 'mac', 'macaroni'];
  ingredients.forEach(ingredient => {
    if (normalized.includes(ingredient)) {
      features.ingredients.push(ingredient);
    }
  });

  // Extract product types
  const productTypes = ['stick', 'ring', 'bite', 'wedge', 'curd', 'wing', 'tender', 'fries', 'ravioli'];
  productTypes.forEach(type => {
    if (normalized.includes(type)) {
      features.productType = type;
    }
  });

  // Extract preparation methods
  const preparations = ['breaded', 'battered', 'fried', 'frozen'];
  preparations.forEach(prep => {
    if (normalized.includes(prep)) {
      features.preparation.push(prep);
    }
  });

  // Determine category
  for (const [category, keywords] of Object.entries(FOOD_CATEGORIES)) {
    if (keywords.some(keyword => normalized.includes(keyword))) {
      features.category = category;
      break;
    }
  }

  return features;
}

// Function to calculate similarity between two products
function calculateProductSimilarity(product1, product2) {
  const features1 = extractProductFeatures(product1);
  const features2 = extractProductFeatures(product2);

  let score = 0;
  let maxScore = 0;

  // Category match (highest weight)
  maxScore += 40;
  if (features1.category && features2.category && features1.category === features2.category) {
    score += 40;
  }

  // Product type match
  maxScore += 30;
  if (features1.productType && features2.productType && features1.productType === features2.productType) {
    score += 30;
  }

  // Ingredient overlap
  maxScore += 20;
  const commonIngredients = features1.ingredients.filter(ing => features2.ingredients.includes(ing));
  if (commonIngredients.length > 0) {
    score += (commonIngredients.length / Math.max(features1.ingredients.length, features2.ingredients.length)) * 20;
  }

  // Preparation method overlap
  maxScore += 10;
  const commonPrep = features1.preparation.filter(prep => features2.preparation.includes(prep));
  if (commonPrep.length > 0) {
    score += (commonPrep.length / Math.max(features1.preparation.length, features2.preparation.length)) * 10;
  }

  return maxScore > 0 ? (score / maxScore) * 100 : 0;
}

// Function to find the core group characteristics
function findCoreGroupCharacteristics(products) {
  const productNames = Object.values(products).filter(name => name && typeof name === 'string');
  if (productNames.length === 0) {
    return {
      dominantCategory: '',
      dominantType: '',
      dominantIngredients: [],
      categoryCount: {},
      typeCount: {},
      ingredientCount: {}
    };
  }

  const features = productNames.map(name => extractProductFeatures(name));

  // Find most common category
  const categories = features.map(f => f.category).filter(c => c);
  const categoryCount = {};
  categories.forEach(cat => {
    categoryCount[cat] = (categoryCount[cat] || 0) + 1;
  });

  const dominantCategory = Object.keys(categoryCount).length > 0
    ? Object.keys(categoryCount).reduce((a, b) => categoryCount[a] > categoryCount[b] ? a : b)
    : '';

  // Find most common product type
  const productTypes = features.map(f => f.productType).filter(t => t);
  const typeCount = {};
  productTypes.forEach(type => {
    typeCount[type] = (typeCount[type] || 0) + 1;
  });

  const dominantType = Object.keys(typeCount).length > 0
    ? Object.keys(typeCount).reduce((a, b) => typeCount[a] > typeCount[b] ? a : b)
    : '';

  // Find most common ingredients
  const allIngredients = features.flatMap(f => f.ingredients);
  const ingredientCount = {};
  allIngredients.forEach(ing => {
    ingredientCount[ing] = (ingredientCount[ing] || 0) + 1;
  });

  const dominantIngredients = Object.keys(ingredientCount)
    .filter(ing => ingredientCount[ing] >= Math.ceil(productNames.length * 0.3)) // At least 30% of products
    .sort((a, b) => ingredientCount[b] - ingredientCount[a]);

  return {
    dominantCategory,
    dominantType,
    dominantIngredients,
    categoryCount,
    typeCount,
    ingredientCount
  };
}

// Function to determine if a product fits the core group
function productFitsGroup(productName, coreCharacteristics, threshold = 60) {
  const features = extractProductFeatures(productName);

  let score = 0;
  let maxScore = 0;

  // Category match
  maxScore += 40;
  if (features.category === coreCharacteristics.dominantCategory) {
    score += 40;
  }

  // Product type match
  maxScore += 30;
  if (features.productType === coreCharacteristics.dominantType) {
    score += 30;
  }

  // Ingredient match
  maxScore += 30;
  const matchingIngredients = features.ingredients.filter(ing =>
    coreCharacteristics.dominantIngredients.includes(ing));
  if (matchingIngredients.length > 0) {
    score += (matchingIngredients.length / coreCharacteristics.dominantIngredients.length) * 30;
  }

  const finalScore = maxScore > 0 ? (score / maxScore) * 100 : 0;
  return finalScore >= threshold;
}

module.exports = {
  normalizeProductName,
  extractProductFeatures,
  calculateProductSimilarity,
  findCoreGroupCharacteristics,
  productFitsGroup,
  FOOD_CATEGORIES,
  CONFLICTING_KEYWORDS
};
