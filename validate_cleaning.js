const fs = require('fs').promises;

async function validateCleaning() {
  console.log('🔍 VALIDATION: Comparing Original vs Cleaned Matches');
  console.log('==================================================\n');

  try {
    // Load both files
    const originalData = JSON.parse(await fs.readFile('matches.json', 'utf8'));
    const cleanedData = JSON.parse(await fs.readFile('matches_cleaned.json', 'utf8'));
    const reportData = JSON.parse(await fs.readFile('cleaning_report.json', 'utf8'));

    console.log('📊 OVERALL STATISTICS');
    console.log('=====================');
    console.log(`Original groups: ${originalData.length.toLocaleString()}`);
    console.log(`Cleaned groups: ${cleanedData.length.toLocaleString()}`);
    console.log(`Groups removed: ${(originalData.length - cleanedData.length).toLocaleString()}`);
    console.log(`Products removed: ${reportData.productsRemoved.toLocaleString()}`);
    console.log(`Reduction: ${((originalData.length - cleanedData.length) / originalData.length * 100).toFixed(1)}%\n`);

    // Show specific examples of improvements
    console.log('🎯 EXAMPLE IMPROVEMENTS');
    console.log('=======================');

    // Find some groups that were significantly cleaned
    const significantlyCleanedGroups = reportData.groupAnalysis
      .filter(group => group.removedProducts.length >= 3 && group.cleanedSize >= 5)
      .slice(0, 5);

    for (const group of significantlyCleanedGroups) {
      const groupIndex = parseInt(group.groupId.replace('group_', '')) - 1;
      const originalGroup = originalData[groupIndex];
      const cleanedGroup = cleanedData.find((_, index) => {
        // Find corresponding cleaned group by checking if it contains any of the remaining products
        const cleanedKeys = Object.keys(cleanedData[index] || {});
        const originalKeys = Object.keys(originalGroup);
        return cleanedKeys.some(key => originalKeys.includes(key));
      });

      if (originalGroup && cleanedGroup) {
        console.log(`\n### ${group.groupId.toUpperCase()}`);
        console.log(`Category: ${group.coreCharacteristics.dominantCategory || 'mixed'}`);
        console.log(`Type: ${group.coreCharacteristics.dominantType || 'mixed'}`);
        console.log(`Size: ${group.originalSize} → ${group.cleanedSize} products`);
        
        console.log('\n✅ KEPT PRODUCTS:');
        Object.entries(cleanedGroup).slice(0, 3).forEach(([key, name]) => {
          console.log(`   ${key}: ${name}`);
        });
        
        console.log('\n❌ REMOVED PRODUCTS:');
        group.removedProducts.slice(0, 3).forEach(product => {
          console.log(`   ${product.key}: ${product.name}`);
        });
      }
    }

    // Show some examples of completely removed groups
    console.log('\n\n🗑️  COMPLETELY REMOVED GROUPS');
    console.log('==============================');
    
    const removedGroups = reportData.groupAnalysis
      .filter(group => group.cleanedSize === 0)
      .slice(0, 3);

    for (const group of removedGroups) {
      const groupIndex = parseInt(group.groupId.replace('group_', '')) - 1;
      const originalGroup = originalData[groupIndex];
      
      console.log(`\n### ${group.groupId.toUpperCase()}`);
      console.log(`Reason: Group too small after cleaning (${group.originalSize} → 0 products)`);
      console.log('Original products:');
      Object.entries(originalGroup).slice(0, 3).forEach(([key, name]) => {
        console.log(`   ${key}: ${name}`);
      });
    }

    console.log('\n\n✨ QUALITY IMPROVEMENTS');
    console.log('========================');
    console.log('The cleaning process successfully:');
    console.log('• Removed products that don\'t match the core group characteristics');
    console.log('• Maintained groups with strong internal consistency');
    console.log('• Eliminated groups that became too small after cleaning');
    console.log('• Preserved the overall structure while improving match quality');

    console.log('\n📁 FILES CREATED');
    console.log('================');
    console.log('• matches_cleaned.json - Your cleaned matches file');
    console.log('• cleaning_report.json - Detailed technical report');
    console.log('• cleaning_summary.md - Human-readable summary');

  } catch (error) {
    console.error('❌ Error during validation:', error.message);
  }
}

// Run validation
validateCleaning().catch(console.error);
